# Тестирование Email Шаблонов

## Примеры запросов для тестирования новых шаблонов

### 1. Тест шаблона напоминания о занятии (class_reminder)

```json
POST /notifications
{
  "title": "Лекция по математическому анализу",
  "message": "Не забудьте взять с собой калькулятор и конспект предыдущей лекции.",
  "type": "info",
  "priority": "normal",
  "target_type": "user",
  "target_value": "YOUR_USER_ID",
  "send_email": true,
  "email_subject": "Напоминание о занятии - Математический анализ",
  "email_template": "class_reminder"
}
```

**Переменные, которые будут заменены:**
- `{{.course_name}}` → "Лекция по математическому анализу"
- `{{.message}}` → "Не забудьте взять с собой калькулятор..."
- `{{.user_name}}` → Имя пользователя из базы данных

### 2. Тест шаблона уведомления о задании (assignment_notification)

```json
POST /notifications
{
  "title": "Курсовая работа по веб-разработке",
  "message": "Создайте полнофункциональное веб-приложение используя современные технологии. Срок сдачи: 15 декабря 2024.",
  "type": "info",
  "priority": "normal",
  "target_type": "user",
  "target_value": "YOUR_USER_ID",
  "send_email": true,
  "email_subject": "Новое задание - Курсовая работа",
  "email_template": "assignment_notification"
}
```

### 3. Тест шаблона уведомления об оценке (grade_notification)

```json
POST /notifications
{
  "title": "Лабораторная работа №5",
  "message": "Отличная работа! Все требования выполнены на высоком уровне. Оценка: 95/100",
  "type": "success",
  "priority": "normal",
  "target_type": "user",
  "target_value": "YOUR_USER_ID",
  "send_email": true,
  "email_subject": "Новая оценка - Лабораторная работа №5",
  "email_template": "grade_notification"
}
```

### 4. Тест приветственного шаблона (welcome_student)

```json
POST /notifications
{
  "title": "Добро пожаловать в EduNite!",
  "message": "Ваш аккаунт успешно создан. Начните с изучения доступных курсов.",
  "type": "info",
  "priority": "normal",
  "target_type": "user",
  "target_value": "YOUR_USER_ID",
  "send_email": true,
  "email_subject": "Добро пожаловать в EduNite!",
  "email_template": "welcome_student"
}
```

### 5. Тест обновленного стандартного шаблона (default_notification)

```json
POST /notifications
{
  "title": "Тестовое уведомление",
  "message": "Это тестовое сообщение для проверки обновленного стандартного шаблона.",
  "type": "info",
  "priority": "normal",
  "target_type": "user",
  "target_value": "YOUR_USER_ID",
  "send_email": true,
  "email_subject": "Тест стандартного шаблона",
  "email_template": "default_notification"
}
```

## Что исправлено:

1. **Обработка шаблонов**: Теперь поддерживается как синтаксис `{{variable}}`, так и `{{.variable}}`
2. **Красивые HTML шаблоны**: Все шаблоны имеют современный дизайн с CSS стилями
3. **Переменные пользователя**: Автоматическая подстановка имени пользователя
4. **Fallback логика**: Если указанный шаблон не найден, используется default_notification
5. **Улучшенная типизация**: Правильная обработка различных типов уведомлений

## Проверка результата:

После отправки уведомления проверьте:
1. **Mailtrap** (sandbox.smtp.mailtrap.io) - там должны появиться письма
2. **HTML версия** - должна отображаться с красивым дизайном
3. **Текстовая версия** - должна быть читаемой без HTML тегов
4. **Переменные** - должны быть корректно заменены на реальные значения

## Возможные проблемы:

1. **Переменные не заменяются**: Проверьте синтаксис `{{variable}}` или `{{.variable}}`
2. **Шаблон не найден**: Убедитесь, что шаблон существует в базе данных
3. **Email не отправляется**: Проверьте настройки SMTP в Mailtrap
