-- Обновление существующих шаблонов и добавление новых для EduNite

-- Обновляем существующие шаблоны
UPDATE email_templates SET 
    html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#007bff;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f8f9fa;padding:30px;border-radius:0 0 8px 8px}.footer{text-align:center;margin-top:20px;color:#666;font-size:12px}</style></head><body><div class="header"><h1>{{.title}}</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><p>{{.message}}</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p><p>Это автоматическое уведомление. Пожалуйста, не отвечайте на это письмо.</p></div></body></html>',
    text_content = 'Здравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\nС уважением,\nКоманда EduNite\n\nЭто автоматическое уведомление. Пожалуйста, не отвечайте на это письмо.',
    variables = '{"title": "Заголовок уведомления", "message": "Текст сообщения", "user_name": "Имя пользователя"}'::jsonb
WHERE name = 'default_notification';

UPDATE email_templates SET 
    html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#dc3545;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0;border:3px solid #721c24}.content{background:#f8d7da;padding:30px;border:2px solid #dc3545;border-top:none;border-radius:0 0 8px 8px}.urgent{color:#721c24;font-weight:bold;font-size:18px}.footer{text-align:center;margin-top:20px;color:#721c24;font-size:12px}</style></head><body><div class="header"><h1>🚨 СРОЧНОЕ УВЕДОМЛЕНИЕ</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="urgent">{{.title}}</div><p>{{.message}}</p><p style="color:#721c24;font-weight:bold;">⚠️ Требуется немедленное внимание!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p><p>Это срочное автоматическое уведомление.</p></div></body></html>',
    text_content = '🚨 СРОЧНОЕ УВЕДОМЛЕНИЕ\n\nЗдравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\n⚠️ Требуется немедленное внимание!\n\nС уважением,\nКоманда EduNite\n\nЭто срочное автоматическое уведомление.',
    variables = '{"title": "Заголовок срочного уведомления", "message": "Текст срочного сообщения", "user_name": "Имя пользователя"}'::jsonb
WHERE name = 'urgent_notification';

UPDATE email_templates SET 
    html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#007bff,#0056b3);color:white;padding:30px;text-align:center;border-radius:8px 8px 0 0}.content{background:#e7f3ff;padding:30px;border:2px solid #007bff;border-top:none;border-radius:0 0 8px 8px}.announcement{background:#fff;padding:20px;border-left:5px solid #007bff;margin:15px 0}.footer{text-align:center;margin-top:20px;color:#0056b3;font-size:12px}</style></head><body><div class="header"><h1>📢 ОБЪЯВЛЕНИЕ</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="announcement"><h2>{{.title}}</h2><p>{{.message}}</p></div><p style="color:#0056b3;font-style:italic;">Это важное объявление от администрации EduNite</p></div><div class="footer"><p>С уважением,<br><strong>Администрация EduNite</strong></p></div></body></html>',
    text_content = '📢 ОБЪЯВЛЕНИЕ\n\nЗдравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\nЭто важное объявление от администрации EduNite\n\nС уважением,\nАдминистрация EduNite',
    variables = '{"title": "Заголовок объявления", "message": "Текст объявления", "user_name": "Имя пользователя"}'::jsonb
WHERE name = 'announcement';

-- Добавляем новые шаблоны
INSERT INTO email_templates (name, subject, html_content, text_content, variables, is_active) VALUES

-- Шаблон для напоминаний о занятиях
('class_reminder', 'Напоминание о занятии - {{.course_name}}', 
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#28a745;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#d4edda;padding:30px;border:2px solid #28a745;border-top:none;border-radius:0 0 8px 8px}.class-info{background:#fff;padding:20px;border-radius:5px;margin:15px 0}.time{font-size:18px;font-weight:bold;color:#155724}.footer{text-align:center;margin-top:20px;color:#155724;font-size:12px}</style></head><body><div class="header"><h1>📚 Напоминание о занятии</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="class-info"><h3>{{.course_name}}</h3><p class="time">🕐 {{.class_time}}</p><p>📍 Аудитория: {{.classroom}}</p><p>👨‍🏫 Преподаватель: {{.teacher_name}}</p></div><p>{{.message}}</p><p style="color:#155724;">Не забудьте взять с собой необходимые материалы!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nНапоминание о занятии:\n{{.course_name}}\n\n🕐 Время: {{.class_time}}\n📍 Аудитория: {{.classroom}}\n👨‍🏫 Преподаватель: {{.teacher_name}}\n\n{{.message}}\n\nНе забудьте взять с собой необходимые материалы!\n\nС уважением,\nКоманда EduNite',
'{"course_name": "Название курса", "class_time": "Время занятия", "classroom": "Номер аудитории", "teacher_name": "Имя преподавателя", "message": "Дополнительная информация", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для уведомлений о заданиях
('assignment_notification', 'Новое задание - {{.assignment_title}}',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#6f42c1;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f3e5f5;padding:30px;border:2px solid #6f42c1;border-top:none;border-radius:0 0 8px 8px}.assignment{background:#fff;padding:20px;border-radius:5px;margin:15px 0;border-left:5px solid #6f42c1}.deadline{color:#dc3545;font-weight:bold;font-size:16px}.footer{text-align:center;margin-top:20px;color:#6f42c1;font-size:12px}</style></head><body><div class="header"><h1>📝 Новое задание</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="assignment"><h3>{{.assignment_title}}</h3><p>📚 Курс: {{.course_name}}</p><p class="deadline">⏰ Срок сдачи: {{.deadline}}</p><p>{{.message}}</p></div><p style="color:#6f42c1;">Удачи в выполнении задания!</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nНовое задание:\n{{.assignment_title}}\n\n📚 Курс: {{.course_name}}\n⏰ Срок сдачи: {{.deadline}}\n\n{{.message}}\n\nУдачи в выполнении задания!\n\nС уважением,\n{{.teacher_name}}',
'{"assignment_title": "Название задания", "course_name": "Название курса", "deadline": "Срок сдачи", "teacher_name": "Имя преподавателя", "message": "Описание задания", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для уведомлений об оценках
('grade_notification', 'Новая оценка - {{.course_name}}',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#fd7e14;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#fff3cd;padding:30px;border:2px solid #fd7e14;border-top:none;border-radius:0 0 8px 8px}.grade{background:#fff;padding:20px;border-radius:5px;margin:15px 0;text-align:center;border:2px solid #fd7e14}.grade-value{font-size:36px;font-weight:bold;color:#fd7e14}.footer{text-align:center;margin-top:20px;color:#fd7e14;font-size:12px}</style></head><body><div class="header"><h1>📊 Новая оценка</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="grade"><h3>{{.assignment_title}}</h3><p>📚 Курс: {{.course_name}}</p><div class="grade-value">{{.grade}}</div><p>{{.feedback}}</p></div><p style="color:#fd7e14;">Продолжайте в том же духе!</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nНовая оценка:\n{{.assignment_title}}\n\n📚 Курс: {{.course_name}}\n📊 Оценка: {{.grade}}\n\n{{.feedback}}\n\nПродолжайте в том же духе!\n\nС уважением,\n{{.teacher_name}}',
'{"assignment_title": "Название задания", "course_name": "Название курса", "grade": "Оценка", "feedback": "Комментарий преподавателя", "teacher_name": "Имя преподавателя", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для регистрации на курс
('course_registration', 'Регистрация на курс - {{.course_name}}',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#20c997;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#d1ecf1;padding:30px;border:2px solid #20c997;border-top:none;border-radius:0 0 8px 8px}.course-info{background:#fff;padding:20px;border-radius:5px;margin:15px 0}.success{color:#155724;font-weight:bold}.footer{text-align:center;margin-top:20px;color:#20c997;font-size:12px}</style></head><body><div class="header"><h1>✅ Успешная регистрация</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="course-info"><h3>{{.course_name}}</h3><p>👨‍🏫 Преподаватель: {{.teacher_name}}</p><p>📅 Начало: {{.start_date}}</p><p>🕐 Расписание: {{.schedule}}</p></div><p class="success">Вы успешно зарегистрированы на курс!</p><p>{{.message}}</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nВы успешно зарегистрированы на курс:\n{{.course_name}}\n\n👨‍🏫 Преподаватель: {{.teacher_name}}\n📅 Начало: {{.start_date}}\n🕐 Расписание: {{.schedule}}\n\n{{.message}}\n\nС уважением,\nКоманда EduNite',
'{"course_name": "Название курса", "teacher_name": "Имя преподавателя", "start_date": "Дата начала", "schedule": "Расписание", "message": "Дополнительная информация", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для отмены занятий
('class_cancellation', 'Отмена занятия - {{.course_name}}',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#dc3545;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f8d7da;padding:30px;border:2px solid:#dc3545;border-top:none;border-radius:0 0 8px 8px}.cancellation{background:#fff;padding:20px;border-radius:5px;margin:15px 0;border-left:5px solid #dc3545}.warning{color:#721c24;font-weight:bold}.footer{text-align:center;margin-top:20px;color:#dc3545;font-size:12px}</style></head><body><div class="header"><h1>❌ Отмена занятия</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="cancellation"><h3>{{.course_name}}</h3><p class="warning">Занятие {{.class_date}} в {{.class_time}} отменено</p><p>📍 Аудитория: {{.classroom}}</p><p><strong>Причина:</strong> {{.reason}}</p></div><p>{{.message}}</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nОтмена занятия:\n{{.course_name}}\n\nЗанятие {{.class_date}} в {{.class_time}} отменено\n📍 Аудитория: {{.classroom}}\n\nПричина: {{.reason}}\n\n{{.message}}\n\nС уважением,\n{{.teacher_name}}',
'{"course_name": "Название курса", "class_date": "Дата занятия", "class_time": "Время занятия", "classroom": "Аудитория", "reason": "Причина отмены", "teacher_name": "Имя преподавателя", "message": "Дополнительная информация", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для экзаменов
('exam_notification', 'Уведомление об экзамене - {{.course_name}}',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#e83e8c;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f8d7da;padding:30px;border:2px solid #e83e8c;border-top:none;border-radius:0 0 8px 8px}.exam-info{background:#fff;padding:20px;border-radius:5px;margin:15px 0;border:2px solid #e83e8c}.important{color:#721c24;font-weight:bold;font-size:16px}.footer{text-align:center;margin-top:20px;color:#e83e8c;font-size:12px}</style></head><body><div class="header"><h1>📋 Экзамен</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="exam-info"><h3>{{.course_name}}</h3><p class="important">📅 Дата: {{.exam_date}}</p><p class="important">🕐 Время: {{.exam_time}}</p><p>📍 Аудитория: {{.classroom}}</p><p>⏱️ Продолжительность: {{.duration}}</p><p><strong>Что взять с собой:</strong> {{.requirements}}</p></div><p>{{.message}}</p><p style="color:#e83e8c;font-weight:bold;">Удачи на экзамене!</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nЭкзамен:\n{{.course_name}}\n\n📅 Дата: {{.exam_date}}\n🕐 Время: {{.exam_time}}\n📍 Аудитория: {{.classroom}}\n⏱️ Продолжительность: {{.duration}}\n\nЧто взять с собой: {{.requirements}}\n\n{{.message}}\n\nУдачи на экзамене!\n\nС уважением,\n{{.teacher_name}}',
'{"course_name": "Название курса", "exam_date": "Дата экзамена", "exam_time": "Время экзамена", "classroom": "Аудитория", "duration": "Продолжительность", "requirements": "Требования", "teacher_name": "Имя преподавателя", "message": "Дополнительная информация", "user_name": "Имя студента"}'::jsonb, true),

-- Шаблон для технических работ
('maintenance_notification', 'Техническое обслуживание системы',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#6c757d;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f8f9fa;padding:30px;border:2px solid #6c757d;border-top:none;border-radius:0 0 8px 8px}.maintenance{background:#fff;padding:20px;border-radius:5px;margin:15px 0;border-left:5px solid #6c757d}.time{font-weight:bold;color:#495057}.footer{text-align:center;margin-top:20px;color:#6c757d;font-size:12px}</style></head><body><div class="header"><h1>🔧 Техническое обслуживание</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="maintenance"><h3>{{.title}}</h3><p class="time">📅 Дата: {{.maintenance_date}}</p><p class="time">🕐 Время: {{.maintenance_time}}</p><p class="time">⏱️ Продолжительность: {{.duration}}</p><p>{{.message}}</p></div><p style="color:#6c757d;">Приносим извинения за временные неудобства.</p></div><div class="footer"><p>С уважением,<br><strong>Техническая поддержка EduNite</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nТехническое обслуживание:\n{{.title}}\n\n📅 Дата: {{.maintenance_date}}\n🕐 Время: {{.maintenance_time}}\n⏱️ Продолжительность: {{.duration}}\n\n{{.message}}\n\nПриносим извинения за временные неудобства.\n\nС уважением,\nТехническая поддержка EduNite',
'{"title": "Заголовок", "maintenance_date": "Дата обслуживания", "maintenance_time": "Время обслуживания", "duration": "Продолжительность", "message": "Описание работ", "user_name": "Имя пользователя"}'::jsonb, true),

-- Шаблон для приветствия новых пользователей
('welcome_student', 'Добро пожаловать в EduNite!',
'<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#007bff,#28a745);color:white;padding:30px;text-align:center;border-radius:8px 8px 0 0}.content{background:#e7f3ff;padding:30px;border:2px solid #007bff;border-top:none;border-radius:0 0 8px 8px}.welcome{background:#fff;padding:20px;border-radius:5px;margin:15px 0}.steps{background:#f8f9fa;padding:15px;border-radius:5px;margin:10px 0}.footer{text-align:center;margin-top:20px;color:#007bff;font-size:12px}</style></head><body><div class="header"><h1>🎓 Добро пожаловать!</h1></div><div class="content"><div class="welcome"><h2>Здравствуйте, {{.user_name}}!</h2><p>Добро пожаловать в образовательную платформу <strong>EduNite</strong>!</p></div><div class="steps"><h3>Первые шаги:</h3><ul><li>📚 Изучите доступные курсы</li><li>📝 Зарегистрируйтесь на интересующие предметы</li><li>📅 Ознакомьтесь с расписанием</li><li>👥 Свяжитесь с преподавателями</li></ul></div><p>{{.message}}</p><p style="color:#007bff;">Желаем успехов в обучении!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p></div></body></html>',
'Здравствуйте, {{.user_name}}!\n\nДобро пожаловать в образовательную платформу EduNite!\n\nПервые шаги:\n📚 Изучите доступные курсы\n📝 Зарегистрируйтесь на интересующие предметы\n📅 Ознакомьтесь с расписанием\n👥 Свяжитесь с преподавателями\n\n{{.message}}\n\nЖелаем успехов в обучении!\n\nС уважением,\nКоманда EduNite',
'{"user_name": "Имя студента", "message": "Дополнительная информация"}'::jsonb, true);
