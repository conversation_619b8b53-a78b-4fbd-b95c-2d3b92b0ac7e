# API для управления программами обучения - Degree Courses

## Что это такое?

Добавлена новая система управления **программами обучения (degree programs)**. Теперь можно:

1. **Связывать курсы с программами** - определять какие курсы входят в конкретную программу обучения
2. **Контролировать доступ студентов** - студенты видят только те курсы, которые входят в их программу
3. **Настраивать обязательность курсов** - отмечать какие курсы обязательные, а какие факультативные
4. **Планировать по семестрам** - указывать в каком семестре должен изучаться курс

## Зачем это нужно?

**Проблема:** Раньше все студенты видели все курсы, что создавало путаницу.

**Решение:** Теперь каждый студент видит только курсы своей программы обучения.

**Пример:**

-  Студент программы "Информатика" видит: Алгоритмы, Базы данных, Веб-разработка
-  Студент программы "Дизайн" видит: Фотошоп, Иллюстратор, UX/UI дизайн

## Как это работает?

1. **Администратор** создает программы обучения (degrees)
2. **Администратор** добавляет курсы к программам через новые API
3. **Студентам** назначается программа обучения
4. **Студенты** видят только курсы своей программы

## Новые эндпоинты

### 1. Добавить курс к программе

**POST** `/degree-courses`

**Headers:**

```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body:**

```json
{
   "degree_id": 1,
   "course_id": 2,
   "is_required": true,
   "semester_number": 1
}
```

**Response (201):**

```json
{
   "id": 1,
   "degree_id": 1,
   "course_id": 2,
   "is_required": true,
   "semester_number": 1,
   "created_at": {
      "seconds": 1749739135,
      "nanos": 975903000
   },
   "updated_at": {
      "seconds": 1749739135,
      "nanos": 975903000
   }
}
```

**cURL:**

```bash
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 2,
    "is_required": true,
    "semester_number": 1
  }'
```

---

### 2. Получить курсы программы

**GET** `/degree-courses/degree/{degree_id}/courses`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response (200):**

```json
{
   "courses": [
      {
         "id": 1,
         "title": "Основы программирования",
         "description": "Введение в программирование на Python",
         "banner_image_url": "course-images/python-basics.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      },
      {
         "id": 2,
         "title": "Веб-разработка",
         "description": "HTML, CSS, JavaScript",
         "banner_image_url": "course-images/web-dev.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      }
   ]
}
```

**cURL:**

```bash
curl -X GET http://localhost:8081/degree-courses/degree/1/courses \
  -H "Authorization: Bearer <admin_token>"
```

---

### 3. Удалить курс из программы

**DELETE** `/degree-courses/degree/{degree_id}/course/{course_id}`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response (200):**

```json
{
   "message": "Course removed from degree successfully"
}
```

**cURL:**

```bash
curl -X DELETE http://localhost:8081/degree-courses/degree/1/course/2 \
  -H "Authorization: Bearer <admin_token>"
```

---

### 4. Получить доступные курсы для студента

**GET** `/students/{user_id}/available-courses`

**Headers:**

```
Authorization: Bearer <token>
```

**Response (200):**

```json
{
   "courses": [
      {
         "id": 1,
         "title": "Доступный курс",
         "description": "Курс доступен студенту",
         "banner_image_url": "course-images/available.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      }
   ]
}
```

**cURL:**

```bash
curl -X GET http://localhost:8081/students/123/available-courses \
  -H "Authorization: Bearer <token>"
```

---

### 5. Получить доступные потоки для студента

**GET** `/students/{user_id}/available-threads`

Возвращает только те потоки, которые доступны студенту на основе его программы обучения. Структура ответа точно такая же, как у `/thread` эндпоинта.

**Response (200):**

```json
[
   {
      "available_slots": 26,
      "booked_slots": 0,
      "course": {
         "id": 1,
         "title": "Golang",
         "description": "Golang course provides...",
         "banner_image_url": ""
      },
      "course_id": 1,
      "created_at": {
         "seconds": 1749731576,
         "nanos": 44856000
      },
      "id": 1,
      "max_students": 26,
      "schedules": [
         {
            "id": 1,
            "thread_id": 1,
            "day_of_week": 1,
            "start_time": "09:00:00",
            "end_time": "10:30:00",
            "location": "501",
            "created_at": {
               "seconds": 1749731576,
               "nanos": 60708000
            },
            "updated_at": {
               "seconds": 1749731576,
               "nanos": 60708000
            }
         }
      ],
      "semester_id": 1,
      "syllabus_url": "",
      "teacher": {
         "id": 3,
         "name": "Ruslan",
         "surname": "Omarov",
         "email": "<EMAIL>"
      },
      "teacher_id": 3,
      "title": "IT-2106",
      "updated_at": {
         "seconds": 1749731576,
         "nanos": 44856000
      }
   }
]
```

**cURL:**

```bash
curl -X GET http://localhost:8081/students/123/available-threads
```

**Особенности:**

-  Возвращает только потоки курсов, к которым у студента есть доступ согласно его программе обучения
-  Если у студента нет доступных курсов, возвращается пустой массив `[]`
-  Структура ответа идентична `/thread` эндпоинту
-  Включает полную информацию о курсе, преподавателе и расписаниях

## Параметры

### Для добавления курса к программе:

-  `degree_id` (int, обязательно) - ID программы обучения
-  `course_id` (int, обязательно) - ID курса
-  `is_required` (bool, опционально) - Обязательный курс (по умолчанию false)
-  `semester_number` (int, опционально) - Номер семестра

## Коды ошибок

-  `400` - Неверные данные запроса
-  `401` - Требуется авторизация
-  `403` - Недостаточно прав или студент не имеет доступа к курсу
-  `404` - Ресурс не найден
-  `409` - Конфликт (например, уже зарегистрирован)
-  `412` - Предварительные условия не выполнены
-  `500` - Внутренняя ошибка сервера

## Важные изменения в обработке ошибок

**Регистрация на поток (`POST /thread/register`):**

-  `403` - Студент не имеет доступа к курсу на основе программы обучения
-  `409` - Студент уже зарегистрирован на поток
-  `404` - Поток не найден
-  `412` - Не выполнены предварительные условия

**Пример ошибки доступа:**

```json
{
   "error": "student does not have access to this course based on their degree program"
}
```

## Доступ

-  Эндпоинты `/degree-courses/*` - только администраторы
-  Эндпоинт `/students/{user_id}/available-courses` - аутентифицированные пользователи

---

# API для управления уведомлениями - Notification Service

## Что это такое?

Добавлена новая система управления **уведомлениями (notifications)**. Теперь можно:

1. **Отправлять уведомления всем пользователям** - массовые объявления
2. **Отправлять уведомления по ролям** - только студентам, только преподавателям
3. **Отправлять уведомления конкретным пользователям** - персональные сообщения
4. **Отправлять уведомления по группам** - студентам определенной программы, курса или потока
5. **Отправлять email уведомления** - дублирование в почту
6. **Планировать отправку** - отложенные уведомления
7. **Отслеживать статус** - прочитано/не прочитано

## Зачем это нужно?

**Проблема:** Нужен способ информирования пользователей о важных событиях, изменениях в расписании, новых заданиях и т.д.

**Решение:** Централизованная система уведомлений с поддержкой email и веб-интерфейса.

**Примеры использования:**

-  Объявления от администрации
-  Уведомления о новых заданиях
-  Изменения в расписании
-  Напоминания о дедлайнах
-  Системные уведомления

## Как это работает?

1. **Администратор/Преподаватель** создает уведомление
2. **Система** определяет получателей на основе типа цели
3. **Система** отправляет уведомления в базу данных
4. **Система** отправляет email (если включено)
5. **Пользователи** получают уведомления в веб-интерфейсе

## Новые эндпоинты

### 1. Создать уведомление

**POST** `/notifications`

**Headers:**

```
Authorization: Bearer <admin_or_teacher_token>
Content-Type: application/json
```

**Request Body:**

```json
{
   "title": "Важное объявление",
   "message": "Завтра занятия отменяются в связи с техническими работами",
   "type": "announcement",
   "priority": "high",
   "target_type": "all",
   "target_value": "",
   "send_email": true,
   "email_subject": "Отмена занятий",
   "scheduled_at": "2024-01-15T10:00:00Z"
}
```

**Response (201):**

```json
{
   "success": true,
   "message": "Notification created successfully",
   "notification": {
      "id": 1,
      "title": "Важное объявление",
      "message": "Завтра занятия отменяются в связи с техническими работами",
      "type": 4,
      "priority": 2,
      "target_type": 0,
      "send_email": true,
      "created_at": {
         "seconds": 1749739135,
         "nanos": 975903000
      }
   }
}
```

**cURL:**

```bash
curl -X POST http://localhost:8081/notifications \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Важное объявление",
    "message": "Завтра занятия отменяются",
    "type": "announcement",
    "priority": "high",
    "target_type": "all",
    "send_email": true
  }'
```

---

### 2. Получить уведомления пользователя

**GET** `/users/{user_id}/notifications`

**Headers:**

```
Authorization: Bearer <token>
```

**Query Parameters:**

-  `page` (int, опционально) - Номер страницы (по умолчанию 1)
-  `limit` (int, опционально) - Количество на странице (по умолчанию 20, максимум 100)
-  `unread_only` (bool, опционально) - Только непрочитанные (по умолчанию false)

**Response (200):**

```json
{
   "notifications": [
      {
         "notification": {
            "id": 1,
            "title": "Новое задание",
            "message": "Добавлено новое задание по курсу Golang",
            "type": 0,
            "priority": 1,
            "target_type": 5,
            "target_value": "1",
            "send_email": true,
            "created_at": {
               "seconds": 1749739135,
               "nanos": 975903000
            }
         },
         "recipient": {
            "id": 1,
            "notification_id": 1,
            "user_id": 123,
            "is_read": false,
            "email_sent": true,
            "created_at": {
               "seconds": 1749739135,
               "nanos": 975903000
            }
         }
      }
   ],
   "total_count": 15,
   "unread_count": 3
}
```

**cURL:**

```bash
curl -X GET "http://localhost:8081/users/123/notifications?page=1&limit=10&unread_only=true" \
  -H "Authorization: Bearer <token>"
```

---

### 3. Отметить уведомление как прочитанное

**PUT** `/notifications/{notification_id}/read`

**Headers:**

```
Authorization: Bearer <token>
```

**Response (200):**

```json
{
   "success": true,
   "message": "Notification marked as read"
}
```

**cURL:**

```bash
curl -X PUT http://localhost:8081/notifications/1/read \
  -H "Authorization: Bearer <token>"
```

---

### 4. Получить все уведомления (админ)

**GET** `/notifications`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Query Parameters:**

-  `page` (int, опционально) - Номер страницы
-  `limit` (int, опционально) - Количество на странице
-  `target_type` (string, опционально) - Фильтр по типу цели
-  `target_value` (string, опционально) - Фильтр по значению цели

**Response (200):**

```json
{
   "notifications": [
      {
         "id": 1,
         "title": "Системное уведомление",
         "message": "Плановые технические работы",
         "type": 0,
         "priority": 2,
         "target_type": 0,
         "send_email": true,
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      }
   ],
   "total_count": 25
}
```

**cURL:**

```bash
curl -X GET "http://localhost:8081/notifications?page=1&limit=20" \
  -H "Authorization: Bearer <admin_token>"
```

---

### 5. Удалить уведомление (админ)

**DELETE** `/notifications/{notification_id}`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response (200):**

```json
{
   "success": true,
   "message": "Notification deleted successfully"
}
```

**cURL:**

```bash
curl -X DELETE http://localhost:8081/notifications/1 \
  -H "Authorization: Bearer <admin_token>"
```

---

### 6. Получить статистику уведомлений

**GET** `/users/{user_id}/notifications/stats`

**Headers:**

```
Authorization: Bearer <token>
```

**Response (200):**

```json
{
   "total_notifications": 25,
   "unread_notifications": 3,
   "read_notifications": 22,
   "email_notifications": 15
}
```

**cURL:**

```bash
curl -X GET http://localhost:8081/users/123/notifications/stats \
  -H "Authorization: Bearer <token>"
```

## Параметры

### Для создания уведомления:

-  `title` (string, обязательно) - Заголовок уведомления
-  `message` (string, обязательно) - Текст сообщения
-  `type` (string, опционально) - Тип уведомления: "info", "warning", "success", "error", "announcement"
-  `priority` (string, опционально) - Приоритет: "low", "normal", "high", "urgent"
-  `target_type` (string, обязательно) - Тип цели: "all", "role", "user", "degree", "course", "thread"
-  `target_value` (string, опционально) - Значение цели (ID пользователя, название роли и т.д.)
-  `send_email` (bool, опционально) - Отправлять email (по умолчанию false)
-  `email_subject` (string, опционально) - Тема письма
-  `email_template` (string, опционально) - Кастомный шаблон письма
-  `scheduled_at` (string, опционально) - Время отправки в формате RFC3339

### Типы целей (target_type):

-  `all` - Всем пользователям
-  `role` - Пользователям с определенной ролью (target_value: "student", "teacher", "admin")
-  `user` - Конкретному пользователю (target_value: ID пользователя)
-  `degree` - Студентам определенной программы (target_value: ID программы)
-  `course` - Студентам определенного курса (target_value: ID курса)
-  `thread` - Студентам определенного потока (target_value: ID потока)

## Коды ошибок

-  `400` - Неверные данные запроса
-  `401` - Требуется авторизация
-  `403` - Недостаточно прав доступа
-  `404` - Ресурс не найден
-  `500` - Внутренняя ошибка сервера

## Доступ

-  **Создание уведомлений** - администраторы и преподаватели
-  **Просмотр всех уведомлений** - только администраторы
-  **Просмотр своих уведомлений** - все аутентифицированные пользователи
-  **Удаление уведомлений** - только администраторы

## Email уведомления

Система поддерживает отправку email уведомлений с использованием SMTP. Для настройки нужно указать:

-  `SMTP_HOST` - SMTP сервер
-  `SMTP_PORT` - Порт SMTP
-  `SMTP_USERNAME` - Имя пользователя
-  `SMTP_PASSWORD` - Пароль
-  `FROM_EMAIL` - Email отправителя
-  `FROM_NAME` - Имя отправителя

Доступны готовые шаблоны:

-  `default_notification` - Обычное уведомление
-  `announcement` - Объявление
-  `urgent_notification` - Срочное уведомление
-  Эндпоинт `/students/{user_id}/available-threads` - публичный доступ (без авторизации)
