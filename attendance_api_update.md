# Обновление API посещаемости

## Изменения в ListAttendance

Эндпоинт `GET /attendance` теперь возвращает информацию о пользователях вместе с записями посещаемости.

### Старый формат ответа:
```json
[
    {
        "id": 262,
        "thread_id": 7,
        "user_id": 2,
        "attendance_date": {
            "seconds": 1738627200
        },
        "status": 1,
        "created_at": {
            "seconds": 1749763614,
            "nanos": 381165000
        },
        "updated_at": {
            "seconds": 1749763614,
            "nanos": 381165000
        }
    }
]
```

### Новый формат ответа:
```json
{
    "records": [
        {
            "id": 262,
            "thread_id": 7,
            "user_id": 2,
            "attendance_date": {
                "seconds": 1738627200
            },
            "status": 1,
            "created_at": {
                "seconds": 1749763614,
                "nanos": 381165000
            },
            "updated_at": {
                "seconds": 1749763614,
                "nanos": 381165000
            },
            "user": {
                "id": 2,
                "name": "Иван",
                "surname": "Иванов",
                "email": "<EMAIL>",
                "role": "student"
            }
        }
    ]
}
```

## Что изменилось:

1. **Добавлена информация о пользователе**: Каждая запись посещаемости теперь содержит объект `user` с полной информацией о студенте
2. **Обертка в объект**: Ответ теперь обернут в объект с полем `records` вместо прямого массива
3. **JOIN с таблицей users**: Запрос к базе данных теперь использует JOIN для получения информации о пользователях
4. **Сортировка по фамилии**: Записи сортируются по фамилии и имени пользователя для удобства

## Структура пользователя:

```json
{
    "id": 2,           // ID пользователя
    "name": "Иван",    // Имя
    "surname": "Иванов", // Фамилия
    "email": "<EMAIL>", // Email
    "role": "student"  // Роль (student, teacher, admin)
}
```

## Пример запроса:

```http
GET /attendance?thread_id=7&attendance_date=2025-02-04
Authorization: Bearer YOUR_TOKEN
```

## Преимущества:

1. **Меньше запросов**: Не нужно делать отдельные запросы для получения информации о каждом пользователе
2. **Полная информация**: Сразу видно имя, фамилию и email студента
3. **Удобная сортировка**: Записи отсортированы по фамилии для удобства просмотра
4. **Готово к использованию**: Данные готовы для отображения в UI без дополнительной обработки

## Обратная совместимость:

⚠️ **Внимание**: Это breaking change! Клиентские приложения нужно обновить для работы с новым форматом ответа.

Если нужна обратная совместимость, можно создать отдельный эндпоинт `/attendance/with-users` или добавить query параметр `include_users=true`.
