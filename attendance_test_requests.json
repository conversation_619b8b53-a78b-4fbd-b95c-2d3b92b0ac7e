{"info": {"name": "Attendance API Tests", "description": "Тестирование обновленного API посещаемости с информацией о пользователях", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "List Attendance with User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/attendance?thread_id={{thread_id}}&attendance_date={{attendance_date}}", "host": ["{{base_url}}"], "path": ["attendance"], "query": [{"key": "thread_id", "value": "{{thread_id}}", "description": "ID потока"}, {"key": "attendance_date", "value": "{{attendance_date}}", "description": "Дата в формате YYYY-MM-DD"}]}, "description": "Получение списка посещаемости с информацией о пользователях"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer token123", "type": "text"}], "url": {"raw": "http://localhost:8080/attendance?thread_id=7&attendance_date=2025-02-04", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["attendance"], "query": [{"key": "thread_id", "value": "7"}, {"key": "attendance_date", "value": "2025-02-04"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"records\": [\n        {\n            \"id\": 262,\n            \"thread_id\": 7,\n            \"user_id\": 2,\n            \"attendance_date\": {\n                \"seconds\": 1738627200\n            },\n            \"status\": 2,\n            \"reason\": \"\",\n            \"created_at\": {\n                \"seconds\": 1749763614,\n                \"nanos\": 381165000\n            },\n            \"updated_at\": {\n                \"seconds\": 1749763614,\n                \"nanos\": 381165000\n            },\n            \"user\": {\n                \"id\": 2,\n                \"name\": \"<PERSON>ва<PERSON>\",\n                \"surname\": \"<PERSON>в<PERSON><PERSON><PERSON>\",\n                \"email\": \"<EMAIL>\",\n                \"role\": \"student\"\n            }\n        },\n        {\n            \"id\": 263,\n            \"thread_id\": 7,\n            \"user_id\": 3,\n            \"attendance_date\": {\n                \"seconds\": 1738627200\n            },\n            \"status\": 3,\n            \"reason\": \"Болезнь\",\n            \"created_at\": {\n                \"seconds\": 1749763615,\n                \"nanos\": 123456000\n            },\n            \"updated_at\": {\n                \"seconds\": 1749763615,\n                \"nanos\": 123456000\n            },\n            \"user\": {\n                \"id\": 3,\n                \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n                \"surname\": \"Петрова\",\n                \"email\": \"<EMAIL>\",\n                \"role\": \"student\"\n            }\n        }\n    ]\n}"}]}, {"name": "Create Attendance Record", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"thread_id\": {{thread_id}},\n    \"user_id\": {{user_id}},\n    \"attendance_date\": \"{{attendance_date}}\",\n    \"status\": 2,\n    \"reason\": \"\"\n}"}, "url": {"raw": "{{base_url}}/attendance", "host": ["{{base_url}}"], "path": ["attendance"]}, "description": "Создание новой записи посещаемости"}}, {"name": "Update Attendance Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": 3,\n    \"reason\": \"Болезнь\"\n}"}, "url": {"raw": "{{base_url}}/attendance/{{attendance_id}}", "host": ["{{base_url}}"], "path": ["attendance", "{{attendance_id}}"]}, "description": "Обновление статуса посещаемости"}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "thread_id", "value": "7", "type": "string"}, {"key": "user_id", "value": "2", "type": "string"}, {"key": "attendance_date", "value": "2025-02-04", "type": "string"}, {"key": "attendance_id", "value": "262", "type": "string"}]}