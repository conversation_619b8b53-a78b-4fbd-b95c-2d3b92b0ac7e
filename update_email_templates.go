package main

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v4/pgxpool"
)

func main() {
	// Database connection
	dbURL := "******************************************************/edunite"
	
	pool, err := pgxpool.Connect(context.Background(), dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer pool.Close()

	// Update existing templates
	fmt.Println("Updating existing email templates...")
	
	// Update default_notification
	_, err = pool.Exec(context.Background(), `
		UPDATE email_templates SET 
			html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#007bff;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f8f9fa;padding:30px;border-radius:0 0 8px 8px}.footer{text-align:center;margin-top:20px;color:#666;font-size:12px}</style></head><body><div class="header"><h1>{{.title}}</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><p>{{.message}}</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p><p>Это автоматическое уведомление. Пожалуйста, не отвечайте на это письмо.</p></div></body></html>',
			text_content = 'Здравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\nС уважением,\nКоманда EduNite\n\nЭто автоматическое уведомление. Пожалуйста, не отвечайте на это письмо.',
			variables = '{"title": "Заголовок уведомления", "message": "Текст сообщения", "user_name": "Имя пользователя"}'::jsonb
		WHERE name = 'default_notification'`)
	if err != nil {
		log.Printf("Error updating default_notification: %v", err)
	} else {
		fmt.Println("✓ Updated default_notification template")
	}

	// Update urgent_notification
	_, err = pool.Exec(context.Background(), `
		UPDATE email_templates SET 
			html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#dc3545;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0;border:3px solid #721c24}.content{background:#f8d7da;padding:30px;border:2px solid #dc3545;border-top:none;border-radius:0 0 8px 8px}.urgent{color:#721c24;font-weight:bold;font-size:18px}.footer{text-align:center;margin-top:20px;color:#721c24;font-size:12px}</style></head><body><div class="header"><h1>🚨 СРОЧНОЕ УВЕДОМЛЕНИЕ</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="urgent">{{.title}}</div><p>{{.message}}</p><p style="color:#721c24;font-weight:bold;">⚠️ Требуется немедленное внимание!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p><p>Это срочное автоматическое уведомление.</p></div></body></html>',
			text_content = '🚨 СРОЧНОЕ УВЕДОМЛЕНИЕ\n\nЗдравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\n⚠️ Требуется немедленное внимание!\n\nС уважением,\nКоманда EduNite\n\nЭто срочное автоматическое уведомление.',
			variables = '{"title": "Заголовок срочного уведомления", "message": "Текст срочного сообщения", "user_name": "Имя пользователя"}'::jsonb
		WHERE name = 'urgent_notification'`)
	if err != nil {
		log.Printf("Error updating urgent_notification: %v", err)
	} else {
		fmt.Println("✓ Updated urgent_notification template")
	}

	// Update announcement
	_, err = pool.Exec(context.Background(), `
		UPDATE email_templates SET 
			html_content = '<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#007bff,#0056b3);color:white;padding:30px;text-align:center;border-radius:8px 8px 0 0}.content{background:#e7f3ff;padding:30px;border:2px solid #007bff;border-top:none;border-radius:0 0 8px 8px}.announcement{background:#fff;padding:20px;border-left:5px solid #007bff;margin:15px 0}.footer{text-align:center;margin-top:20px;color:#0056b3;font-size:12px}</style></head><body><div class="header"><h1>📢 ОБЪЯВЛЕНИЕ</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="announcement"><h2>{{.title}}</h2><p>{{.message}}</p></div><p style="color:#0056b3;font-style:italic;">Это важное объявление от администрации EduNite</p></div><div class="footer"><p>С уважением,<br><strong>Администрация EduNite</strong></p></div></body></html>',
			text_content = '📢 ОБЪЯВЛЕНИЕ\n\nЗдравствуйте, {{.user_name}}!\n\n{{.title}}\n\n{{.message}}\n\nЭто важное объявление от администрации EduNite\n\nС уважением,\nАдминистрация EduNite',
			variables = '{"title": "Заголовок объявления", "message": "Текст объявления", "user_name": "Имя пользователя"}'::jsonb
		WHERE name = 'announcement'`)
	if err != nil {
		log.Printf("Error updating announcement: %v", err)
	} else {
		fmt.Println("✓ Updated announcement template")
	}

	// Add new templates
	fmt.Println("\nAdding new email templates...")

	templates := []struct {
		name        string
		subject     string
		htmlContent string
		textContent string
		variables   string
	}{
		{
			name:    "class_reminder",
			subject: "Напоминание о занятии - {{.course_name}}",
			htmlContent: `<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#28a745;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#d4edda;padding:30px;border:2px solid #28a745;border-top:none;border-radius:0 0 8px 8px}.class-info{background:#fff;padding:20px;border-radius:5px;margin:15px 0}.time{font-size:18px;font-weight:bold;color:#155724}.footer{text-align:center;margin-top:20px;color:#155724;font-size:12px}</style></head><body><div class="header"><h1>📚 Напоминание о занятии</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="class-info"><h3>{{.course_name}}</h3><p class="time">🕐 {{.class_time}}</p><p>📍 Аудитория: {{.classroom}}</p><p>👨‍🏫 Преподаватель: {{.teacher_name}}</p></div><p>{{.message}}</p><p style="color:#155724;">Не забудьте взять с собой необходимые материалы!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p></div></body></html>`,
			textContent: `Здравствуйте, {{.user_name}}!\n\nНапоминание о занятии:\n{{.course_name}}\n\n🕐 Время: {{.class_time}}\n📍 Аудитория: {{.classroom}}\n👨‍🏫 Преподаватель: {{.teacher_name}}\n\n{{.message}}\n\nНе забудьте взять с собой необходимые материалы!\n\nС уважением,\nКоманда EduNite`,
			variables: `{"course_name": "Название курса", "class_time": "Время занятия", "classroom": "Номер аудитории", "teacher_name": "Имя преподавателя", "message": "Дополнительная информация", "user_name": "Имя студента"}`,
		},
		{
			name:    "assignment_notification",
			subject: "Новое задание - {{.assignment_title}}",
			htmlContent: `<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#6f42c1;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#f3e5f5;padding:30px;border:2px solid #6f42c1;border-top:none;border-radius:0 0 8px 8px}.assignment{background:#fff;padding:20px;border-radius:5px;margin:15px 0;border-left:5px solid #6f42c1}.deadline{color:#dc3545;font-weight:bold;font-size:16px}.footer{text-align:center;margin-top:20px;color:#6f42c1;font-size:12px}</style></head><body><div class="header"><h1>📝 Новое задание</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="assignment"><h3>{{.assignment_title}}</h3><p>📚 Курс: {{.course_name}}</p><p class="deadline">⏰ Срок сдачи: {{.deadline}}</p><p>{{.message}}</p></div><p style="color:#6f42c1;">Удачи в выполнении задания!</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>`,
			textContent: `Здравствуйте, {{.user_name}}!\n\nНовое задание:\n{{.assignment_title}}\n\n📚 Курс: {{.course_name}}\n⏰ Срок сдачи: {{.deadline}}\n\n{{.message}}\n\nУдачи в выполнении задания!\n\nС уважением,\n{{.teacher_name}}`,
			variables: `{"assignment_title": "Название задания", "course_name": "Название курса", "deadline": "Срок сдачи", "teacher_name": "Имя преподавателя", "message": "Описание задания", "user_name": "Имя студента"}`,
		},
		{
			name:    "grade_notification",
			subject: "Новая оценка - {{.course_name}}",
			htmlContent: `<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:#fd7e14;color:white;padding:20px;text-align:center;border-radius:8px 8px 0 0}.content{background:#fff3cd;padding:30px;border:2px solid #fd7e14;border-top:none;border-radius:0 0 8px 8px}.grade{background:#fff;padding:20px;border-radius:5px;margin:15px 0;text-align:center;border:2px solid #fd7e14}.grade-value{font-size:36px;font-weight:bold;color:#fd7e14}.footer{text-align:center;margin-top:20px;color:#fd7e14;font-size:12px}</style></head><body><div class="header"><h1>📊 Новая оценка</h1></div><div class="content"><p>Здравствуйте, {{.user_name}}!</p><div class="grade"><h3>{{.assignment_title}}</h3><p>📚 Курс: {{.course_name}}</p><div class="grade-value">{{.grade}}</div><p>{{.feedback}}</p></div><p style="color:#fd7e14;">Продолжайте в том же духе!</p></div><div class="footer"><p>С уважением,<br><strong>{{.teacher_name}}</strong></p></div></body></html>`,
			textContent: `Здравствуйте, {{.user_name}}!\n\nНовая оценка:\n{{.assignment_title}}\n\n📚 Курс: {{.course_name}}\n📊 Оценка: {{.grade}}\n\n{{.feedback}}\n\nПродолжайте в том же духе!\n\nС уважением,\n{{.teacher_name}}`,
			variables: `{"assignment_title": "Название задания", "course_name": "Название курса", "grade": "Оценка", "feedback": "Комментарий преподавателя", "teacher_name": "Имя преподавателя", "user_name": "Имя студента"}`,
		},
		{
			name:    "welcome_student",
			subject: "Добро пожаловать в EduNite!",
			htmlContent: `<html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#007bff,#28a745);color:white;padding:30px;text-align:center;border-radius:8px 8px 0 0}.content{background:#e7f3ff;padding:30px;border:2px solid #007bff;border-top:none;border-radius:0 0 8px 8px}.welcome{background:#fff;padding:20px;border-radius:5px;margin:15px 0}.steps{background:#f8f9fa;padding:15px;border-radius:5px;margin:10px 0}.footer{text-align:center;margin-top:20px;color:#007bff;font-size:12px}</style></head><body><div class="header"><h1>🎓 Добро пожаловать!</h1></div><div class="content"><div class="welcome"><h2>Здравствуйте, {{.user_name}}!</h2><p>Добро пожаловать в образовательную платформу <strong>EduNite</strong>!</p></div><div class="steps"><h3>Первые шаги:</h3><ul><li>📚 Изучите доступные курсы</li><li>📝 Зарегистрируйтесь на интересующие предметы</li><li>📅 Ознакомьтесь с расписанием</li><li>👥 Свяжитесь с преподавателями</li></ul></div><p>{{.message}}</p><p style="color:#007bff;">Желаем успехов в обучении!</p></div><div class="footer"><p>С уважением,<br><strong>Команда EduNite</strong></p></div></body></html>`,
			textContent: `Здравствуйте, {{.user_name}}!\n\nДобро пожаловать в образовательную платформу EduNite!\n\nПервые шаги:\n📚 Изучите доступные курсы\n📝 Зарегистрируйтесь на интересующие предметы\n📅 Ознакомьтесь с расписанием\n👥 Свяжитесь с преподавателями\n\n{{.message}}\n\nЖелаем успехов в обучении!\n\nС уважением,\nКоманда EduNite`,
			variables: `{"user_name": "Имя студента", "message": "Дополнительная информация"}`,
		},
	}

	for _, tmpl := range templates {
		_, err = pool.Exec(context.Background(), `
			INSERT INTO email_templates (name, subject, html_content, text_content, variables, is_active) 
			VALUES ($1, $2, $3, $4, $5::jsonb, true)
			ON CONFLICT (name) DO UPDATE SET
				subject = EXCLUDED.subject,
				html_content = EXCLUDED.html_content,
				text_content = EXCLUDED.text_content,
				variables = EXCLUDED.variables,
				updated_at = CURRENT_TIMESTAMP`,
			tmpl.name, tmpl.subject, tmpl.htmlContent, tmpl.textContent, tmpl.variables)
		
		if err != nil {
			log.Printf("Error adding/updating template %s: %v", tmpl.name, err)
		} else {
			fmt.Printf("✓ Added/Updated %s template\n", tmpl.name)
		}
	}

	fmt.Println("\n🎉 Email templates update completed!")
}
